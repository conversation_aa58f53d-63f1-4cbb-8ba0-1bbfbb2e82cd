# file: /home/<USER>/DEV/lib2docScrape/src/backends/playwright_backend.py
# hypothesis_version: 6.131.20

[0.0, 1.0, 2.0, 30.0, 60.0, 100, 200, 304, 400, 408, 500, 503, 800, 1000, 1280, '403 Forbidden', '404 Not Found', 'Access Denied', 'Circuit breaker open', 'Page Not Found', 'Service Unavailable', 'URL already crawled', 'Unknown error', 'a', 'assets', 'backend', 'browser_type', 'cached', 'chromium', 'content', 'error', 'error_details', 'firefox', 'headers', 'headings', 'height', 'href', 'html', 'html.parser', 'javascript_enabled', 'links', 'metadata', 'networkidle', 'playwright', 'screenshot', 'screenshots', 'server', 'status', 'structure', 'success', 'text', 'text/html', 'timeout', 'title', 'url', 'webkit', 'width']