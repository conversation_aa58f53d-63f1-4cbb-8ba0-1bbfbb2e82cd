# file: /home/<USER>/DEV/lib2docScrape/src/organizers/doc_organizer.py
# hypothesis_version: 6.131.20

[0.3, 1000, '/', '\\w+', 'a', 'an', 'and', 'api', 'are', 'at', 'be', 'been', 'but', 'by', 'code', 'code_blocks', 'concept', 'content', 'could', 'demo', 'did', 'do', 'doc', 'documentation', 'does', 'endpoint', 'errors', 'example', 'external', 'for', 'formatted_content', 'graphql', 'guide', 'had', 'has', 'have', 'headings', 'how-to', 'howto', 'http://', 'https://', 'in', 'internal', 'introduction', 'is', 'it', 'its', 'language', 'links', 'meta_tags', 'of', 'on', 'or', 'overview', 'reference', 'rest', 'sample', 'search_indices', 'should', 'text', 'that', 'the', 'their', 'them', 'these', 'they', 'this', 'those', 'title', 'to', 'tutorial', 'type', 'uncategorized', 'url', 'v', 'version_id', 'was', 'were', 'will', 'with', 'would']